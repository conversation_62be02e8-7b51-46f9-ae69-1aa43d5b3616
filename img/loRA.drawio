<mxfile host="65bd71144e">
    <diagram id="0CQdcv8-_7EHusp5LPR4" name="Page-1">
        <mxGraphModel dx="1693" dy="636" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="217" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;endArrow=none;endFill=0;strokeWidth=3;" edge="1" parent="1" source="211">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-255.5" y="530" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="211" value="" style="rounded=1;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;arcSize=6;fontFamily=Helvetica;fontSize=12;resizable=1;fillColor=#b1ddf0;strokeColor=#10739e;" vertex="1" parent="1">
                    <mxGeometry x="-380" y="340" width="250" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="153" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=6;container=0;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="1">
                    <mxGeometry x="-350" y="526" width="250" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="158" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=6;container=0;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="1">
                    <mxGeometry x="-380" y="560" width="250" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="157" value="&lt;b&gt;Layer 2&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-180" y="560" width="50" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="156" value="&lt;b&gt;Rank：6&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f9f7ed;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-380" y="560" width="70" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="183" style="edgeStyle=none;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;dashed=1;endArrow=none;endFill=0;" edge="1" parent="1" source="142" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="142" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=6;container=0;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="1">
                    <mxGeometry x="-390" y="572" width="250" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="151" value="&lt;b&gt;Rank：7&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f9f7ed;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-390" y="572" width="70" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="184" style="edgeStyle=none;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;dashed=1;endArrow=none;endFill=0;" edge="1" parent="1" source="150" target="181">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="150" value="&lt;b&gt;Layer 1&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-190" y="572" width="50" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="" style="rounded=1;whiteSpace=wrap;html=1;arcSize=3;fillColor=#eeeeee;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-40" y="490" width="370" height="360" as="geometry"/>
                </mxCell>
                <mxCell id="179" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;endArrow=classic;startArrow=classic;endFill=1;strokeWidth=3;" edge="1" parent="1" source="11" target="165">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="17" y="790"/>
                            <mxPoint x="263" y="790"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="whiteSpace=wrap;html=1;aspect=fixed;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="-20" y="600" width="148" height="148" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="&lt;b&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;Low-rank B&lt;/font&gt;&lt;/b&gt;" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;direction=west;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
                    <mxGeometry x="150" y="600" width="150" height="43" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="line;strokeWidth=3;direction=south;html=1;" vertex="1" parent="1">
                    <mxGeometry x="138" y="790" width="10" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="&lt;b&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;Sublayer Input x&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="113" y="812" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="42" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;endArrow=none;endFill=0;" edge="1" parent="1" source="38" target="11">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" style="edgeStyle=none;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=3;" edge="1" parent="1" source="38">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="143" y="530" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="" style="shape=sumEllipse;perimeter=ellipsePerimeter;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="128" y="550" width="30" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="&lt;font&gt;Masked W&lt;span style=&quot;background-color: transparent;&quot;&gt;eights&lt;/span&gt;&lt;/font&gt;&lt;div&gt;&lt;font&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;W&lt;sub&gt;0&lt;/sub&gt;&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=17;" vertex="1" parent="1">
                    <mxGeometry x="-31" y="650" width="170" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="Sublayer Output h" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;" vertex="1" parent="1">
                    <mxGeometry x="108" y="500" width="70" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=6;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-400" y="586" width="250" height="220" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="&lt;b&gt;Value Projection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-389" y="766" width="104" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="&lt;b&gt;Key Projection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-267.5" y="766" width="104" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="&lt;b&gt;Query Projection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cdeb8b;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-268.5" y="718" width="105" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="&lt;b&gt;Output Projection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-389" y="718" width="105" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="55" value="&lt;b&gt;U&lt;/b&gt;&lt;b style=&quot;background-color: transparent;&quot;&gt;p Projection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF99FF;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-327.5" y="666" width="105" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="&lt;b style=&quot;background-color: transparent;&quot;&gt;Gate Projection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5CCFF;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-267.5" y="616" width="105" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="&lt;b style=&quot;background-color: transparent;&quot;&gt;Down Projection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#CCFFFF;strokeColor=#36393d;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-390" y="616" width="105" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="&lt;b&gt;Layer 0&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-200" y="586" width="50" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="141" value="&lt;b&gt;Rank：6&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f9f7ed;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-400" y="586" width="70" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="159" value="&lt;b&gt;Rank：5&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f9f7ed;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-350" y="526" width="70" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="161" value="&lt;b&gt;Layer n&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;arcSize=22;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-150" y="526" width="50" height="14" as="geometry"/>
                </mxCell>
                <mxCell id="162" value="&lt;b&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;... ...&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;rotation=-45;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-150" y="530" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="165" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;b&gt;Low-rank A&lt;/b&gt;&lt;/font&gt;" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;direction=west;flipV=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
                    <mxGeometry x="150" y="703" width="150" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="171" style="edgeStyle=isometricEdgeStyle;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=3;elbow=vertical;endArrow=none;endFill=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-521.8199999999999" y="250" as="sourcePoint"/>
                        <mxPoint x="-571.8199999999999" y="310" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-596.38" y="257"/>
                            <mxPoint x="-726.8199999999999" y="197"/>
                            <mxPoint x="-566.8199999999999" y="247"/>
                            <mxPoint x="-596.8199999999999" y="287"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="174" value="&lt;b&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;... ...&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;rotation=-45;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-150" y="736" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="181" value="&lt;b&gt;&lt;font style=&quot;font-size: 13px;&quot;&gt;Layer i&amp;nbsp;&lt;/font&gt;&lt;/b&gt;&lt;div&gt;&lt;b&gt;&lt;font style=&quot;font-size: 13px;&quot;&gt;&amp;nbsp;Projection j&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=22;container=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
                    <mxGeometry x="-40" y="490" width="110" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="185" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.212;entryY=0.968;entryDx=0;entryDy=0;entryPerimeter=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=3;endArrow=none;endFill=0;" edge="1" parent="1" source="38" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="186" value="" style="shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;strokeColor=#36393d;arcSize=6;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;fillColor=#eeeeee;direction=south;" vertex="1" parent="1">
                    <mxGeometry x="170" y="683" width="110" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="187" value="&lt;b&gt;&lt;font style=&quot;font-size: 13px;&quot;&gt;Rank&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;" vertex="1" parent="1">
                    <mxGeometry x="195" y="664" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="189" style="edgeStyle=isometricEdgeStyle;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=3;elbow=vertical;startArrow=classic;startFill=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-179.9996004384197" y="526.0001463619956" as="sourcePoint"/>
                        <mxPoint x="-229.9996004384197" y="586.0001463619956" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="-249.9996004384197" y="523.0001463619956"/>
                            <mxPoint x="-384.9996004384197" y="473.0001463619956"/>
                            <mxPoint x="-224.9996004384197" y="523.0001463619956"/>
                            <mxPoint x="-254.9996004384197" y="563.0001463619956"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="195" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=3;shape=flexArrow;" edge="1" parent="1" source="192" target="194">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="206" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=3;" edge="1" parent="1" source="192" target="200">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="192" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;b&gt;Dense Output&lt;/b&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-235" y="240" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="207" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=3;" edge="1" parent="1" source="193" target="200">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="193" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;b&gt;Sparse Output&lt;/b&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;container=0;" vertex="1" parent="1">
                    <mxGeometry x="125" y="240" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="198" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;shape=flexArrow;strokeWidth=3;" edge="1" parent="1" source="194" target="193">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="194" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;b&gt;Giraffe Sparse&lt;/b&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-55" y="230" width="130" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="216" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=3;" edge="1" parent="1" source="200" target="211">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="200" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;b&gt;RL Calculator&lt;/b&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-55" y="320" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="214" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=2;" edge="1" parent="1" source="212" target="213">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-315" y="360"/>
                            <mxPoint x="-192" y="360"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="218" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=2;endArrow=none;endFill=0;shape=link;" edge="1" parent="1" source="212" target="213">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="212" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;b&gt;Rank Allocation&lt;/b&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-367.5" y="382.5" width="105" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="215" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;fontSize=12;fontColor=default;resizable=1;strokeWidth=2;" edge="1" parent="1" source="213" target="212">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="-192" y="460"/>
                            <mxPoint x="-315" y="460"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="213" value="&lt;div&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;b&gt;Rank&lt;/b&gt;&lt;/span&gt;&lt;/div&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;b&gt;Optimizer&lt;/b&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;container=0;" vertex="1" parent="1">
                    <mxGeometry x="-245" y="382.5" width="105" height="55" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>