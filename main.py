import torch
import torch.nn as nn
import gc
import json
import time
import copy
from CKA import cka
import numpy as np
from peft.tuners.lora import LoraLayer
from tqdm import tqdm

from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    TrainingArguments,
    )
from peft import (
    LoraConfig,
    prepare_model_for_kbit_training,
    get_peft_model
    )
from datasets import load_dataset, Dataset, DatasetDict
from trl import SFTTrainer
from lib.eval import eval_ppl
from lib.prune import check_sparsity, prune_sparsegpt, prune_wanda, prune_giraffe, get_feature_map, allocate_ranks
from lib.data import get_loaders
import argparse

parser = argparse.ArgumentParser()
parser.add_argument('--model', type=str, help='LLaMA model')
parser.add_argument('--seed', type=int, default=4, help='Seed for sampling the calibration data.')
parser.add_argument('--nsamples', type=int, default=8, help='Number of calibration samples.')
parser.add_argument('--sparsity_ratio', type=float, default=0, help='Sparsity level')
parser.add_argument("--sparsity_type", type=str, choices=["unstructured", "4:8", "2:4"])
parser.add_argument("--prune_method", type=str, choices=["wanda", "sparsegpt", "giraffe"])
sparsity_group = parser.add_mutually_exclusive_group()
sparsity_group.add_argument("--use_balanced_sparsity", action="store_true", default=True, help="Use balanced sparsity allocation to reduce deviation (default)")
sparsity_group.add_argument("--use_dynamic_sparsity", action="store_true", help="Use dynamic sparsity allocation with larger layer differences")
parser.add_argument("--cache_dir", default="llm_weights", type=str )
parser.add_argument('--use_variant', action="store_true", help="whether to use the wanda variant described in the appendix")
parser.add_argument('--save', type=str, default=None, help='Path to save results.')
parser.add_argument('--save_model', type=str, default=None, help='Path to save the pruned model.')
parser.add_argument('--quantify_model', action='store_true', help='Enable low-rank decomposition quantification.')
args = parser.parse_args()

print("正在加载模型...")
print(args.model)
start_time = time.time()

model = AutoModelForCausalLM.from_pretrained(
    args.model,
    torch_dtype=torch.bfloat16,
    device_map={"": 0}
)

model_seqlen = 128
model = prepare_model_for_kbit_training(model)
model.enable_input_require_grads()
model.config.use_cache = False
model.config.pretraining_tp = 1

# tokenizer
tokenizer = AutoTokenizer.from_pretrained(
    args.model,
    use_fast=False,
    padding_side='right',
    trust_remote_code=True,
    add_eos_token=True,
    add_bos_token=True
    )

tokenizer.pad_token = tokenizer.eos_token

peft_config = LoraConfig(
    r=8,
    lora_alpha=16,
    target_modules=[
        'q_proj',
        'k_proj',
        'v_proj',
        'o_proj',
        'gate_proj',
        'up_proj',
        'down_proj',
        ],
    bias="none",
    lora_dropout=0.1,
    task_type="CAUSAL_LM"
    )

model = get_peft_model(model, peft_config)

c4_dataloader, _ = get_loaders(
    "c4",
    nsamples=args.nsamples,
    seed=args.seed,
    seqlen=model_seqlen,
    tokenizer=tokenizer,
)

if '7' in args.model or '8' in args.model:
    calib_nsamples=64
elif '13' in args.model:
    calib_nsamples=32
else:
    calib_nsamples=args.nsamples

calib_dataloader, _ = get_loaders(
    "c4",
    nsamples=calib_nsamples,
    seed=args.seed,
    seqlen=model_seqlen,
    tokenizer=tokenizer,
)

# Handling n:m sparsity
if args.sparsity_type == "2:4":
    prune_n, prune_m = 2, 4
elif args.sparsity_type == "4:8":
    prune_n, prune_m = 4, 8
else:
    prune_n, prune_m = 0, 0

model.base_model.model.seqlen = model_seqlen
model.base_model.model.eval()

model.merge_adapter()
dense_feature = get_feature_map(args, model.base_model.model, tokenizer, dataloader=c4_dataloader)
model.unmerge_adapter()

dense_feature = [feature.cpu() for feature in dense_feature]

torch.cuda.empty_cache()
gc.collect()

iters = 5
iter_mean_rank = 6
split_size = 1000

for prune_iter in range(1, iters+1):
    model.merge_adapter()

    if args.prune_method=='wanda':
        recon_loss = prune_wanda(args, model.base_model.model, tokenizer, prune_n=prune_n, dense_feature = dense_feature,
                                prune_m=prune_m, dataloader=c4_dataloader, calib_dataloader = calib_dataloader,
                                prune_iter=prune_iter, iters=iters)
    elif args.prune_method=='sparsegpt':
        recon_loss = prune_sparsegpt(args, model.base_model.model, tokenizer, prune_n=prune_n, dense_feature = dense_feature,
                                prune_m=prune_m, dataloader=c4_dataloader, calib_dataloader = calib_dataloader,
                                prune_iter=prune_iter, iters=iters)
    elif args.prune_method=='giraffe':
        recon_loss = prune_giraffe(args, model.base_model.model, tokenizer, prune_n=prune_n, dense_feature = dense_feature,
                                prune_m=prune_m, dataloader=c4_dataloader, calib_dataloader = calib_dataloader,
                                prune_iter=prune_iter, iters=iters)
    else:
        raise ValueError(f"未知的剪枝方法: {args.prune_method}")
    print("重构损失: ", [f"{loss:.4f}" for loss in recon_loss])

    model.unmerge_adapter()

    iter_rank = allocate_ranks(np.array(recon_loss), rank=iter_mean_rank)
    layer_idx = 0
    for name, module in model.named_modules():
        if isinstance(module, LoraLayer):
            setattr(module, 'iter_rank', iter_rank[layer_idx])
        if 'post_attention_layernorm' in name:
            layer_idx += 1

    iter_mean_rank += 1

    if '7' in args.model or '8' in args.model:
        train_batch_size=4
    elif '13' in args.model:
        train_batch_size=2
    else:
        train_batch_size=1

    training_arguments = TrainingArguments(
        output_dir= './results',
        num_train_epochs= 1,
        per_device_train_batch_size=train_batch_size,
        gradient_accumulation_steps=2,
        optim = 'paged_adamw_8bit',
        save_steps= 1000,
        logging_steps= 20,
        learning_rate= 2e-4,
        weight_decay= 0.001,
        fp16=False,
        bf16=False,
        max_grad_norm= 0.3,
        max_steps= -1,
        warmup_ratio= 0.3,
        group_by_length= True,
        lr_scheduler_type= 'linear',
        report_to="none"
        )

    original_dataset = 'vicgalle/alpaca-gpt4'
    start_index = prune_iter * split_size
    end_index = start_index + split_size
    split = f"train[{start_index}:{end_index}]"
    if prune_iter==5:
        split = f"train[4000:10000]"
    dataset = load_dataset(original_dataset, split=split)

    # Set Supervised Finetuning Trainer (SFTTrainer) parameters
    trainer = SFTTrainer(
        model=model,
        train_dataset=dataset,
        peft_config=peft_config,
        max_seq_length=128,
        dataset_text_field='text',
        tokenizer=tokenizer,
        args=training_arguments,
        packing=False
        )

    # Train model
    trainer.train()

    model.merge_adapter()
    ################################################################
    print("*"*30)
    print("训练后稀疏性检查...")
    sparsity_ratio = check_sparsity(model.base_model.model)
    print(f"稀疏性检查结果: {sparsity_ratio:.4f}")
    print("*"*30)
    ################################################################
    model.unmerge_adapter()

end_time = time.time()
elapsed_time = (end_time - start_time)/60
print(f"总运行时间: {elapsed_time:.2f} 分钟")

# 合并LoRA权重到基础模型
print("正在合并LoRA权重...")
model = model.merge_and_unload()

# 模型量化：使用统一的量化模块
if args.quantify_model:
    from lib.model_quantifier import quantify_model
    import numpy as np
    
    print("\n" + "="*50)
    print("开始模型量化...")
    print("="*50)
    print(f"模型类型: {type(model)}")

    # 创建量化器参数
    sparsity_threshold = 1e-6  # 稀疏性阈值
    min_compression_ratio = 1.2  # 最小压缩比要求
    quantization_bits = 8  # 量化位数
    min_sparsity_for_sparse = 0.3  # 使用稀疏压缩的最小稀疏度

    # 使用高效无损量化参数
    efficient_min_compression_ratio = 1.8  # 适中的压缩比要求
    efficient_min_sparsity = 0.3  # 合理的稀疏度要求

    print(f"量化参数:")
    print(f"  量化方法: INT8量化")
    print(f"  稀疏性阈值: {sparsity_threshold}")
    print(f"  最小压缩比: {efficient_min_compression_ratio}")
    print(f"  量化位数: {quantization_bits}")
    print(f"  稀疏压缩阈值: {efficient_min_sparsity}")

    # 执行模型量化
    start_time = time.time()
    quantified_model, quantification_report = quantify_model(
        model=model,
        sparsity_threshold=sparsity_threshold,
        min_compression_ratio=efficient_min_compression_ratio,
        quantization_bits=quantization_bits,
        min_sparsity_for_sparse=efficient_min_sparsity
    )
    quantification_time = time.time() - start_time

    # 显示量化报告
    print("\n" + "="*50)
    print("模型量化完成!")
    print("="*50)
    print(f"量化时间: {quantification_time:.2f} 秒")
    print(f"量化层数: {quantification_report['compressed_layers']}")
    print(f"跳过层数: {quantification_report['skipped_layers']}")
    if quantification_report.get('error_layers', 0) > 0:
        print(f"错误层数: {quantification_report['error_layers']}")
    
    print(f"原始内存: {quantification_report['total_original_memory_mb']:.1f} MB (FP32)")
    print(f"量化后内存: {quantification_report['total_compressed_memory_mb']:.1f} MB (INT8)")
    print(f"内存减少: {quantification_report['memory_reduction']:.2%}")
    print(f"内存压缩比: {quantification_report['memory_compression_ratio']:.2f}x")
    
    # 理论上INT8量化应该有4倍内存压缩比
    theoretical_compression = 4.0  # FP32 -> INT8
    actual_compression = quantification_report['memory_compression_ratio']
    print(f"理论压缩比: {theoretical_compression:.1f}x (FP32->INT8)")
    print(f"实际压缩比: {actual_compression:.2f}x")

    # 量化后内存清理
    print("清理量化过程中的临时内存...")
    torch.cuda.empty_cache()
    import gc
    gc.collect()

    print("="*50)

    # 保存量化报告
    if args.save_model is not None:
        quantified_save_path = args.save_model

        # 只保存量化报告，不保存模型
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            elif isinstance(obj, (np.integer, np.floating)):
                return float(obj)
            elif isinstance(obj, torch.Tensor):
                return obj.tolist()
            else:
                return obj

        json_report = convert_types(quantification_report)
        with open(f"{quantified_save_path}/quantification_report.json", 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)

        print(f"量化报告已保存到: {quantified_save_path}/quantification_report.json")

    # 更新模型引用为量化后的模型
    model = quantified_model

    # 最终内存清理
    print("执行最终内存清理...")
    torch.cuda.empty_cache()
    gc.collect()

    # 显示当前GPU内存使用情况
    if torch.cuda.is_available():
        current_memory = torch.cuda.memory_allocated() / 1024 / 1024
        max_memory = torch.cuda.max_memory_allocated() / 1024 / 1024
        print(f"当前GPU内存使用: {current_memory:.1f} MB")
        print(f"峰值GPU内存使用: {max_memory:.1f} MB")
    gc.collect()

# 保存最终的稀疏模型
if args.save_model is not None:
    print(f"正在保存稀疏模型到: {args.save_model}")

    import os
    import shutil
    os.makedirs(args.save_model, exist_ok=True)

    # 保存模型权重和配置（不包含适配器文件）
    model.save_pretrained(args.save_model, safe_serialization=False)
    tokenizer.save_pretrained(args.save_model)

    # 确保配置文件存在且正确
    config_path = os.path.join(args.save_model, "config.json")
    if not os.path.exists(config_path):
        original_config_path = os.path.join(args.model, "config.json")
        if os.path.exists(original_config_path):
            shutil.copy2(original_config_path, config_path)

    # 修正配置文件中的模型名称
    if os.path.exists(config_path):
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        config["_name_or_path"] = os.path.basename(args.save_model)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

    print(f"模型保存完成: {args.save_model}")

device = torch.device("cuda:0")
dataset = 'wikitext2'

model.seqlen = 4096

ppl = eval_ppl(model, tokenizer, dataset, device)
print(f"\n在 {dataset} 数据集上的困惑度 (PPL): {ppl}\n")