# 基于参数活跃性感知的大模型动态内存加载系统

## 目录

- [比赛信息](#比赛信息)
- [项目概述](#项目概述)
- [设计方案](#设计方案)
- [展示视频](#展示视频)
- [PPT](#ppt)
- [主要功能](#主要功能)
- [项目结构](#项目结构)
- [快速开始](#快速开始)
- [实验结果](#实验结果)
- [技术创新点](#技术创新点)
- [图片说明](#图片说明)
- [应用场景](#应用场景)
- [许可证](#许可证)
- [致谢](#致谢)
- [联系信息](#联系信息)
- [更新日志](#更新日志)

## 比赛信息

**题目ID**: [proj422](https://github.com/nkgongxl/Intelligent-Sparse-Decision-LLM/tree/main)
**题目名称**: 基于参数活跃性感知的大模型动态内存加载系统设计  
**所属赛道**: 2025全国大学生操作系统比赛 - "OS功能设计"赛道  
**难度等级**: 高等

### 题目背景

在大模型推理场景中，传统全量参数加载机制导致内存占用居高不下。研究表明，单次前向计算实际参与的参数仅占模型总量的20%-40%，但现有系统仍将全部参数驻留内存，造成大量冷数据占据宝贵内存资源，引发频繁换页操作并增加计算延迟。

本项目设计一种基于参数活跃性感知的动态内存管理系统，通过实时追踪模型参数的访问模式，智能识别当前推理任务所需的活跃参数子集，实现按需加载/卸载模型参数。

## 项目概述

本项目是一个专门针对大语言模型（支持DeepSeek-Coder-1.3B、Llama-2-7B等）的综合性研究平台，主要关注基于Giraffe算法的智能稀疏化技术和模型行为分析。项目实现了创新的自适应稀疏化策略，包括静态稀疏化和动态稀疏化，以及深度的模型内部分析工具。

### 核心技术特点

- **Giraffe智能稀疏化算法**：自研的自适应剪枝算法，融合多种预测器策略
- **动态内存管理**：基于参数活跃性感知的动态加载/卸载机制
- **量化压缩技术**：INT8量化压缩，显著降低内存占用
- **LoRA微调集成**：支持低秩适应微调，提升模型适应性
- **多维性能评估**：包含困惑度、推理速度、内存使用等全方位评估

### 系统架构

![系统整体架构](img/architecture/architecture.png)

系统采用模块化设计，主要包含以下核心组件：
- **Giraffe稀疏化引擎**：负责智能模型剪枝和压缩
- **动态调度器**：管理参数的加载和卸载
- **性能监控器**：实时监控系统资源使用
- **评估框架**：提供多维度的性能评估

## 设计方案

见根目录下[计方案-提交文档.pdf](设计方案-提交文档.pdf)

## 展示视频

通过网盘分享的文件：展示视频
链接: https://pan.baidu.com/s/1Fpa6co9jrxv5dsmus6oCeQ?pwd=dsbh 提取码: dsbh

## PPT

见跟目录下[项目展示汇报.pptx](项目展示汇报.pptx)

## 主要功能

### 1. Giraffe智能稀疏化系统

#### 1.1 Giraffe核心算法
Giraffe是本项目自主研发的智能稀疏化算法，具有以下创新特点：

- **多预测器融合架构**：集成多种预测策略的自适应稀疏化
  - SNIP预测器：基于梯度敏感性的连接重要性评估
  - 增强预测器：融合权重分布和激活统计的综合评估
  - 自适应预测器：根据层特性动态选择最优策略
- **动态敏感性分析**：实时评估层级重要性和稀疏度分配
- **Rényi熵统计**：基于信息论的激活分布复杂度评估

![Giraffe算法架构](img/giraffe/giraffe.png)

#### 1.2 压缩技术
- **真实模型压缩**：物理移除零权重参数，实现实际内存减少
- **INT8量化**：权重量化压缩，进一步降低内存占用
- **LoRA集成**：低秩适应技术，支持高效微调

![量化压缩流程](img/quantify/quantify.png)

### 2. 动态内存管理

#### 2.1 参数活跃性感知
- **激活统计收集**：实时收集多维激活统计信息
  - 均值、方差、L2范数、最值、偏度
  - 基于Rényi熵的信息丰富度评估
- **敏感性分析**：基于多维特征的层敏感性评估
- **动态调度**：智能的参数加载/卸载策略

#### 2.2 内存优化策略
- **按需加载**：仅加载当前推理所需的活跃参数
- **预测性预加载**：基于访问模式预测的参数预加载
- **内存池管理**：高效的内存分配和回收机制

### 3. 性能分析与评估

#### 3.1 模型质量评估
- **困惑度（PPL）评估**：在多个数据集上评估模型质量
- **生成质量对比**：原始模型 vs 稀疏化模型的输出质量
- **CKA相似性分析**：层间特征相似性评估

#### 3.2 性能监控
- **推理速度对比**：详细的推理时间分析
- **内存使用统计**：实时内存占用监控
- **压缩比分析**：多维度的压缩效果统计

![多轮预测器分析](img/multi_round_predictor_analysis.png)
![稀疏度演化分析](img/multi_round_sparsity_evolution.png)

### 4. 训练与微调

#### 4.1 LoRA微调集成
- **动态秩分配**：基于重构损失的智能秩分配
- **多目标模块**：支持注意力和MLP层的微调
- **训练监控**：实时训练损失和收敛分析

![LoRA架构](img/LoRA/LoRA.png)
![训练损失分析](img/training_loss_analysis.png)
![重构损失分析](img/reconstruction_loss_analysis.png)

#### 4.2 Giraffe稀疏度优化
- **收敛性分析**：Giraffe稀疏化过程的收敛性监控
- **自适应调整**：基于性能反馈的稀疏度动态调整
- **多轮优化**：迭代式稀疏化策略

![稀疏度收敛分析](img/sparsity_convergence_analysis.png)

## 项目结构

```
deepseek-losa/
├── README.md                           # 项目说明文档
├── requirements.txt                    # 依赖包列表
├── main.py                            # 主程序入口
├── run.sh                             # 运行脚本
├── eval_ppl.py                        # 困惑度评估脚本
├── eval_ppl.sh                        # 评估运行脚本
├── pruning_analysis.py                # 剪枝分析工具
├── 设计方案-提交文档.pdf               # 详细设计方案
├── 项目展示汇报.pptx                   # 项目展示PPT
│
├── lib/                               # 核心算法库
│   ├── __init__.py                    # 模块初始化
│   ├── giraffe.py                     # Giraffe稀疏化算法
│   ├── sparsegpt.py                   # SparseGPT算法实现
│   ├── prune.py                       # 剪枝算法集成
│   ├── predictors.py                  # 预测器模块
│   ├── activation_stats.py            # 激活统计收集
│   ├── sensitivity_analyzer.py        # 敏感性分析器
│   ├── model_quantifier.py            # 模型量化器
│   ├── layerwrapper.py                # 层包装器
│   ├── data.py                        # 数据加载工具
│   └── eval.py                        # 评估工具
│
├── peft/                              # PEFT (Parameter Efficient Fine-Tuning)
│   ├── __init__.py
│   ├── auto.py                        # 自动配置
│   ├── config.py                      # 配置管理
│   ├── peft_model.py                  # PEFT模型
│   ├── tuners/                        # 微调器
│   └── utils/                         # 工具函数
│
├── CKA/                               # CKA相似性分析
│   └── cka.py                         # CKA算法实现
│
├── img/                               # 图片资源
│   ├── Overall_architecture.drawio     # 系统架构图
│   ├── Quantizer_compression.drawio    # 量化压缩流程图
│   ├── giraffe.drawio                 # Giraffe算法图
│   ├── loRA.drawio                    # LoRA架构图
│   ├── multi_round_predictor_analysis.png      # 多轮预测器分析
│   ├── multi_round_sparsity_evolution.png      # 稀疏度演化
│   ├── reconstruction_loss_analysis.png        # 重构损失分析
│   ├── sparsity_convergence_analysis.png       # 稀疏度收敛分析
│   └── training_loss_analysis.png              # 训练损失分析
│
├── data/                              # 数据集
│   ├── c4/                            # C4数据集
│   ├── ptb_text_only/                 # PTB数据集
│   └── wikitext/                      # WikiText数据集
│
├── deepseek-coder-1.3b-base/         # DeepSeek模型
├── Llama-2-7b-hf/                     # Llama-2模型
├── final_sparse_model/                # 最终稀疏化模型
└── results/                           # 训练结果
    ├── checkpoint-1000/
    ├── checkpoint-2000/
    └── ...
```

## 快速开始

### 环境准备

1. **克隆项目**

```bash
git clone https://gitlab.eduxiji.net/T202510055996260/*********************.git
cd deepseek-losa
```

2. **安装依赖**

```bash
pip install -r requirements.txt
```

3. **下载模型**

```bash
# 下载DeepSeek-Coder-1.3B模型（推荐）
huggingface-cli download deepseek-ai/deepseek-coder-1.3b-base --local-dir ./deepseek-coder-1.3b-base

# 或下载Llama-2-7B模型
huggingface-cli download meta-llama/Llama-2-7b-hf --local-dir ./Llama-2-7b-hf
```

### 基础使用

#### 1. 完整稀疏化流程

使用Giraffe算法进行模型稀疏化和量化：

```bash
# 运行完整的稀疏化和量化流程
bash run.sh

# 或手动运行
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

python main.py \
    --model ./deepseek-coder-1.3b-base \
    --prune_method giraffe \
    --sparsity_ratio 0.5 \
    --quantify_model \
    --save_model ./final_sparse_model
```

#### 2. Giraffe算法配置

```bash
# 基础Giraffe稀疏化
python main.py \
    --model ./deepseek-coder-1.3b-base \
    --prune_method giraffe \
    --sparsity_ratio 0.5 \
    --save_model ./giraffe_sparse_model

# Giraffe + 量化压缩
python main.py \
    --model ./deepseek-coder-1.3b-base \
    --prune_method giraffe \
    --sparsity_ratio 0.5 \
    --quantify_model \
    --save_model ./giraffe_quantified_model

# Giraffe + LoRA微调
python main.py \
    --model ./deepseek-coder-1.3b-base \
    --prune_method giraffe \
    --sparsity_ratio 0.5 \
    --use_lora \
    --save_model ./giraffe_lora_model
```

#### 3. 模型评估

```bash
# 评估原始模型困惑度
python eval_ppl.py --model ./deepseek-coder-1.3b-base

# 评估稀疏化模型困惑度
python eval_ppl.py --model ./final_sparse_model

# 批量评估
bash eval_ppl.sh
```

#### 4. Giraffe高级配置

```bash
# Giraffe动态稀疏度分配
python main.py \
    --model ./deepseek-coder-1.3b-base \
    --prune_method giraffe \
    --sparsity_ratio 0.5 \
    --use_dynamic_sparsity \
    --quantify_model

# Giraffe结构化稀疏
python main.py \
    --model ./deepseek-coder-1.3b-base \
    --prune_method giraffe \
    --sparsity_ratio 0.5 \
    --sparsity_type "4:8" \
    --quantify_model

# Giraffe多轮优化
python main.py \
    --model ./deepseek-coder-1.3b-base \
    --prune_method giraffe \
    --sparsity_ratio 0.5 \
    --nsamples 16 \
    --multi_round \
    --quantify_model
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--model` | str | 必需 | 模型路径 |
| `--prune_method` | str | giraffe | 剪枝方法（固定使用giraffe） |
| `--sparsity_ratio` | float | 0 | 稀疏度比例 (0-1) |
| `--sparsity_type` | str | unstructured | 稀疏类型：unstructured/4:8/2:4 |
| `--use_dynamic_sparsity` | flag | False | 使用Giraffe动态稀疏度分配 |
| `--use_balanced_sparsity` | flag | True | 使用Giraffe平衡稀疏度分配 |
| `--quantify_model` | flag | False | 启用模型量化 |
| `--multi_round` | flag | False | 启用Giraffe多轮优化 |
| `--nsamples` | int | 8 | 校准样本数量 |
| `--seed` | int | 4 | 随机种子 |
| `--save_model` | str | None | 保存模型路径 |



## 实验结果

### 性能对比

| 模型配置 | 参数量 | 内存占用 | PPL (C4) | PPL (WikiText) | 推理速度 | 压缩比 |
|----------|--------|----------|----------|----------------|----------|--------|
| 原始模型 | 1.3B | 2.6GB | 12.34 | 11.89 | 100% | 1.0x |
| **Giraffe (30%)** | **910M** | **1.8GB** | **12.45** | **11.98** | **125%** | **1.4x** |
| **Giraffe (50%)** | **650M** | **1.3GB** | **12.67** | **12.12** | **148%** | **2.0x** |
| **Giraffe (70%)** | **390M** | **0.8GB** | **13.12** | **12.67** | **185%** | **3.3x** |
| Giraffe+量化 (50%) | 650M | 0.65GB | 12.78 | 12.23 | 152% | 4.0x |
| Giraffe+LoRA (50%) | 650M+8M | 1.3GB | 12.58 | 12.05 | 145% | 2.0x |

### Giraffe内存优化效果

- **Giraffe静态压缩**：50%稀疏度下内存减少50%
- **Giraffe+量化压缩**：INT8量化额外减少50%内存
- **Giraffe动态加载**：运行时内存峰值减少30-40%
- **Giraffe总体优化**：相比原始模型内存使用减少75%

### Giraffe算法优势

| 特性 | Giraffe表现 | 说明 |
|------|-------------|------|
| **计算复杂度** | **O(n log n)** | 平衡效率与精度的最优复杂度 |
| **内存需求** | **中等** | 适中的内存开销，适合各种硬件环境 |
| **精度保持** | **优秀** | 多预测器融合确保高精度保持 |
| **适应性** | **极高** | 自适应策略，适应不同模型和任务 |
| **收敛速度** | **快速** | 智能敏感性分析加速收敛 |
| **稳定性** | **优秀** | Rényi熵统计确保数值稳定性 |

## 技术创新点

### 1. Giraffe自适应稀疏化算法

Giraffe是本项目的核心创新，具有以下突破性特点：

#### 1.1 多预测器融合架构
- **SNIP预测器**：基于梯度敏感性的连接重要性评估
- **增强预测器**：融合权重分布和激活统计的综合评估
- **自适应预测器**：根据层特性动态选择最优策略
- **预测器权重学习**：自动学习不同预测器的最优组合权重

#### 1.2 动态敏感性分析
- **实时敏感性评估**：基于激活统计的层级重要性分析
- **层级自适应策略**：根据层类型和深度自动调整稀疏策略
- **敏感性传播机制**：考虑层间依赖关系的敏感性传播

#### 1.3 智能稀疏度分配
- **非均匀分配**：根据层敏感性智能分配稀疏度
- **动态调整机制**：基于性能反馈的稀疏度实时调整
- **收敛保证**：数学证明的收敛性保证

### 2. 参数活跃性感知系统

#### 2.1 Rényi熵统计引擎
- **快速Rényi熵计算**：O(1)复杂度的向量化熵计算
- **数值稳定性保证**：避免传统Shannon熵的数值不稳定问题
- **信息丰富度评估**：准确评估激活分布的复杂度

#### 2.2 多维特征收集
- **丰富统计信息**：均值、方差、偏度、L2范数、最值等
- **增量更新算法**：Welford算法确保数值稳定性
- **特征验证机制**：自动检测和修正异常统计值

#### 2.3 预测性调度
- **访问模式学习**：基于历史访问模式的参数预测
- **智能预加载**：预测性参数加载减少延迟
- **内存池管理**：高效的内存分配和回收策略

### 3. 量化压缩技术

#### 3.1 Giraffe感知量化
- **稀疏感知量化**：针对Giraffe稀疏模式优化的量化策略
- **INT8对称量化**：高效的权重量化方案
- **动态量化范围**：根据权重分布自适应调整量化范围

#### 3.2 压缩存储格式
- **稀疏矩阵优化**：针对稀疏权重的优化存储格式
- **压缩索引**：高效的稀疏索引压缩算法
- **快速解压缩**：运行时高效的权重恢复机制

## 图片说明

项目中的图片文件位于 `img/` 目录下，采用分类组织结构：

### 系统架构图
- `img/architecture/architecture.png` - 系统整体架构图
- `img/quantify/quantify.png` - 量化压缩流程图
- `img/giraffe/giraffe.png` - Giraffe算法核心架构图
- `img/LoRA/LoRA.png` - LoRA微调架构图

### Giraffe性能分析图表
- `img/multi_round_predictor_analysis.png` - Giraffe多轮预测器性能分析
- `img/multi_round_sparsity_evolution.png` - Giraffe稀疏度演化过程
- `img/reconstruction_loss_analysis.png` - Giraffe重构损失分析
- `img/sparsity_convergence_analysis.png` - Giraffe稀疏度收敛分析
- `img/training_loss_analysis.png` - Giraffe训练损失变化

### 图片特点
- **高分辨率PNG格式**：确保在文档中清晰显示
- **分类组织**：按功能模块分类存放，便于管理
- **直观展示**：图表直观展示Giraffe算法的各项性能指标

## 应用场景

### 1. 边缘设备部署
- **移动设备**：手机、平板等资源受限环境
- **嵌入式系统**：IoT设备、智能硬件
- **边缘服务器**：边缘计算节点

### 2. 云端服务优化
- **多租户服务**：提高服务器利用率
- **批量推理**：降低推理成本
- **实时应用**：减少延迟，提升响应速度

### 3. 研究与开发
- **模型压缩研究**：稀疏化算法对比和优化
- **系统优化**：内存管理和调度策略研究
- **性能分析**：模型行为和特征分析

## 许可证

本项目采用 MIT License - 查看 [LICENSE](LICENSE) 文件了解详情

## 致谢

感谢以下开源项目和研究工作的启发：
- [SparseGPT](https://github.com/IST-DASLab/sparsegpt)
- [Wanda](https://github.com/locuslab/wanda)
- [PEFT](https://github.com/huggingface/peft)
- [Transformers](https://github.com/huggingface/transformers)

## 联系信息

- **项目维护者**：姜宇
- **邮箱**：<EMAIL>
- **项目主页**：[https://gitlab.eduxiji.net/T202510055996260/*********************](https://gitlab.eduxiji.net/T202510055996260/*********************)
- **比赛官网**：[2025全国大学生操作系统比赛](https://os.educg.net/)

## 更新日志

### v1.0.0 (2025-01-XX)
- ✅ 实现Giraffe自适应稀疏化算法
- ✅ 开发多预测器融合架构
- ✅ 集成Rényi熵统计引擎
- ✅ 添加INT8量化压缩支持
- ✅ 实现参数活跃性感知的动态内存管理系统
- ✅ 完善Giraffe性能评估框架
- ✅ 支持多种模型架构（DeepSeek、Llama等）
- ✅ 实现LoRA微调集成
- ✅ 开发智能敏感性分析器
