"""
预测器模块

包含各种权重重要性预测算法：
1. 增强预测器
2. <PERSON>预测器
3. SNIP预测器
"""

import torch


class PredictorRegistry:
    """预测器注册表，管理所有可用的预测器"""
    
    @staticmethod
    def dolphin_predictor(activation_stats, weight_matrix, layer_info):
        """
        优化预测器 - 专注于最有效的重要性评估

        核心策略：
        1. 以Wanda的核心公式为基础：权重×激活统计
        2. 加强对重要权重的保护，减少过度剪枝
        3. 简化特征融合，专注于最有效的指标
        4. 保守的剪枝策略，优先保持模型性能

        Args:
            activation_stats: 激活统计信息字典
            weight_matrix: 当前层的权重矩阵 [out_features, in_features]
            layer_info: 层信息（layer_id, layer_name等）

        Returns:
            importance_scores: 每个权重的重要性分数 [out_features, in_features]
        """
        # 获取核心激活统计特征
        l2_norm = activation_stats['l2_norm']      # [in_features] - 激活幅度
        variance = activation_stats['var']         # [in_features] - 激活变异性

        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]

        # === 1. 核心Wanda分量：权重×激活幅度 ===
        # 这是最有效的重要性指标，直接采用Wanda的成功公式
        activation_scale = torch.sqrt(l2_norm + 1e-8).unsqueeze(0)
        wanda_component = weight_abs * activation_scale

        # === 2. 方差增强分量：权重×方差 ===
        # 高方差表示激活变化大，对应权重更敏感，需要保护
        var_scale = torch.sqrt(variance + 1e-8).unsqueeze(0)
        variance_component = weight_abs * var_scale

        # === 3. 权重幅度分量：直接权重大小 ===
        # 大权重通常更重要，需要保护
        magnitude_component = weight_abs

        # === 4. 增强的权重保护机制 ===
        # 对于重要权重给予更强的保护，避免过度剪枝

        # 4.1 基于权重大小的保护
        weight_percentile_90 = torch.quantile(weight_abs.flatten(), 0.90)
        large_weight_mask = weight_abs >= weight_percentile_90

        # 4.2 基于激活的保护
        activation_percentile_90 = torch.quantile(l2_norm, 0.90)
        important_activation_mask = l2_norm >= activation_percentile_90
        important_activation_mask = important_activation_mask.unsqueeze(0)

        # 4.3 综合保护因子
        protection_factor = torch.ones_like(weight_abs)
        protection_factor[large_weight_mask] *= 2.0  # 大权重保护
        protection_factor = protection_factor * (1.0 + important_activation_mask.float())  # 重要激活保护

        # 4.4 应用保护到Wanda分量
        protected_wanda = wanda_component * protection_factor

        # === 5. 层级自适应权重分配 ===
        layer_id = layer_info.get('layer_id', 0)
        layer_name = layer_info.get('layer_name', '')

        # 更加保守的权重分配策略，强调权重保护
        if 'q_proj' in layer_name or 'k_proj' in layer_name:
            # 注意力层：强化保护，这些层对性能影响最大
            w_wanda, w_var, w_mag, w_protected = 0.30, 0.20, 0.15, 0.35
        elif 'v_proj' in layer_name:
            # Value层：重视保护和Wanda核心
            w_wanda, w_var, w_mag, w_protected = 0.35, 0.20, 0.15, 0.30
        elif 'o_proj' in layer_name:
            # 输出层：强化保护
            w_wanda, w_var, w_mag, w_protected = 0.30, 0.25, 0.15, 0.30
        elif 'gate_proj' in layer_name:
            # 门控层：平衡处理，适度保护
            w_wanda, w_var, w_mag, w_protected = 0.35, 0.30, 0.15, 0.20
        elif 'up_proj' in layer_name:
            # Up投影：适度保护
            w_wanda, w_var, w_mag, w_protected = 0.35, 0.25, 0.15, 0.25
        elif 'down_proj' in layer_name:
            # Down投影：仍然保护重要权重
            w_wanda, w_var, w_mag, w_protected = 0.40, 0.25, 0.20, 0.15
        else:
            # 默认权重分配：强化保护
            w_wanda, w_var, w_mag, w_protected = 0.35, 0.25, 0.15, 0.25

        # === 6. 分量融合 ===
        # 使用稳健归一化
        def robust_normalize(tensor):
            percentile_95 = torch.quantile(tensor.flatten(), 0.95)
            return tensor / (percentile_95 + 1e-8)

        wanda_norm = robust_normalize(wanda_component)
        var_norm = robust_normalize(variance_component)
        mag_norm = robust_normalize(magnitude_component)
        protected_norm = robust_normalize(protected_wanda)

        # 多特征加权融合
        importance_scores = (w_wanda * wanda_norm +
                           w_var * var_norm +
                           w_mag * mag_norm +
                           w_protected * protected_norm)

        # === 7. 保守的最终分数处理 ===
        # 专注于保护重要权重，避免过度剪枝

        # 7.1 基于层类型的保守调整
        layer_multiplier = 1.0
        if 'q_proj' in layer_name or 'k_proj' in layer_name:
            # 关键注意力层：保守处理
            layer_multiplier = 0.9  # 降低剪枝激进程度
        elif 'v_proj' in layer_name:
            # Value层：标准处理
            layer_multiplier = 1.0
        elif 'o_proj' in layer_name:
            # 输出层：保守处理
            layer_multiplier = 0.95
        elif 'gate_proj' in layer_name:
            # 门控层：标准处理
            layer_multiplier = 1.0
        elif 'up_proj' in layer_name:
            # Up投影：标准处理
            layer_multiplier = 1.0
        elif 'down_proj' in layer_name:
            # Down投影：可以稍微激进
            layer_multiplier = 1.1

        # 7.2 基于层深度的保守调整
        if layer_id < 6:  # 早期层更保守
            layer_multiplier *= 0.8
        elif layer_id > 18:  # 后期层稍微激进
            layer_multiplier *= 1.05

        # 7.3 应用保守调整
        importance_scores = importance_scores * layer_multiplier

        # 7.4 添加基础保护，避免过小的分数
        min_protection = 0.1 * torch.mean(importance_scores)
        importance_scores = importance_scores + min_protection

        # 7.5 确保非负性
        importance_scores = torch.clamp(importance_scores, min=1e-6)

        return importance_scores

    @staticmethod
    def wanda_predictor(activation_stats, weight_matrix, layer_info=None):
        """
        Wanda预测器 - 融合SparseGPT二阶信息的增强版本

        核心改进：
        1. 保持Wanda的核心优势：权重×激活统计的简洁有效性
        2. 融合SparseGPT的二阶信息：使用方差近似Hessian对角线
        3. 多尺度激活特征：L2范数 + 方差 + 动态范围
        4. 权重结构感知：考虑权重分布特性和相互作用
        5. 层级自适应：根据层类型调整权重
        6. 数值稳定性：借鉴SparseGPT的稳定性策略

        Returns:
            importance_scores: 权重重要性分数
        """
        # 获取激活统计特征
        l2_norm = activation_stats['l2_norm']      # [in_features]
        variance = activation_stats['var']         # [in_features]
        max_val = activation_stats['max_val']      # [in_features]
        min_val = activation_stats['min_val']      # [in_features]

        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]
        weight_squared = weight_matrix ** 2    # [out_features, in_features]

        # === 1. 经典Wanda分量：权重×L2范数 ===
        # 保持Wanda的核心成功公式
        l2_scale = torch.sqrt(l2_norm + 1e-8).unsqueeze(0)
        wanda_classic = weight_abs * l2_scale

        # === 2. SparseGPT启发的二阶分量：权重²/方差 ===
        # 融合SparseGPT的重要性计算思想
        hessian_approx = variance + 1e-8  # 用方差近似Hessian对角线
        sparsegpt_inspired = weight_squared / hessian_approx.unsqueeze(0)

        # === 3. 增强的方差分量：权重×方差 ===
        # 高方差表示激活变化大，对应权重更敏感
        var_scale = torch.sqrt(variance + 1e-8).unsqueeze(0)
        variance_component = weight_abs * var_scale

        # === 4. 动态范围分量：权重×激活范围 ===
        # 大动态范围表示激活能表达更丰富的信息
        dynamic_range = torch.clamp(max_val - min_val, min=1e-8)
        range_scale = torch.sqrt(dynamic_range).unsqueeze(0)
        range_component = weight_abs * range_scale

        # === 5. 权重结构感知（借鉴SparseGPT的权重相互作用思想）===
        # 5.1 输出维度相对重要性
        weight_mean_out = torch.mean(weight_abs, dim=1, keepdim=True)
        output_relative = weight_abs / (weight_mean_out + 1e-8)

        # 5.2 输入维度相对重要性
        weight_mean_in = torch.mean(weight_abs, dim=0, keepdim=True)
        input_relative = weight_abs / (weight_mean_in + 1e-8)

        # 5.3 结构交互项
        structure_component = output_relative * input_relative

        # === 6. 层级自适应权重分配 ===
        if layer_info:
            layer_name = layer_info.get('layer_name', '')
            if 'q_proj' in layer_name or 'k_proj' in layer_name:
                # 注意力层：平衡Wanda经典和SparseGPT二阶信息
                w_wanda, w_sparsegpt, w_var, w_range, w_struct = 0.35, 0.25, 0.20, 0.15, 0.05
            elif 'v_proj' in layer_name:
                # Value层：重视Wanda经典方法
                w_wanda, w_sparsegpt, w_var, w_range, w_struct = 0.40, 0.20, 0.20, 0.15, 0.05
            elif 'gate_proj' in layer_name:
                # 门控层：重视SparseGPT二阶信息（更精确的敏感性）
                w_wanda, w_sparsegpt, w_var, w_range, w_struct = 0.25, 0.35, 0.25, 0.10, 0.05
            elif 'o_proj' in layer_name:
                # 输出层：重视结构相互作用
                w_wanda, w_sparsegpt, w_var, w_range, w_struct = 0.30, 0.25, 0.20, 0.15, 0.10
            elif 'down_proj' in layer_name:
                # 下投影：重视结构分析（便于激进剪枝）
                w_wanda, w_sparsegpt, w_var, w_range, w_struct = 0.25, 0.25, 0.20, 0.15, 0.15
            else:
                # 默认权重：平衡各项特征
                w_wanda, w_sparsegpt, w_var, w_range, w_struct = 0.35, 0.25, 0.20, 0.15, 0.05
        else:
            w_wanda, w_sparsegpt, w_var, w_range, w_struct = 0.35, 0.25, 0.20, 0.15, 0.05

        # === 7. 分量融合 ===
        # 使用稳健归一化（借鉴SparseGPT的数值稳定性）
        def robust_normalize(tensor):
            percentile_95 = torch.quantile(tensor.flatten(), 0.95)
            return tensor / (percentile_95 + 1e-8)

        wanda_norm = robust_normalize(wanda_classic)
        sparsegpt_norm = robust_normalize(sparsegpt_inspired)
        var_norm = robust_normalize(variance_component)
        range_norm = robust_normalize(range_component)
        struct_norm = robust_normalize(structure_component)

        # 融合Wanda和SparseGPT的优点
        importance_scores = (w_wanda * wanda_norm +
                           w_sparsegpt * sparsegpt_norm +
                           w_var * var_norm +
                           w_range * range_norm +
                           w_struct * struct_norm)

        # === 8. 最终稳定性处理（借鉴SparseGPT的阻尼机制）===
        # 添加小的阻尼项以避免数值不稳定
        damp_factor = 0.01 * torch.mean(importance_scores)
        importance_scores = importance_scores + damp_factor

        # 确保非负性
        importance_scores = torch.clamp(importance_scores, min=1e-8)

        return importance_scores
    

    @staticmethod
    def snip_predictor(activation_stats, weight_matrix, layer_info=None):
        """
        SNIP预测器 - 融合Wanda和SparseGPT优点的增强版本

        核心改进：
        1. 保持SNIP的梯度敏感性核心：基于方差的梯度近似
        2. 融合Wanda的激活感知：权重×激活统计的有效组合
        3. 借鉴SparseGPT的二阶信息：使用Hessian近似提升精度
        4. 多维梯度近似：方差 + 偏度 + 熵 + L2范数
        5. 权重敏感性分析：考虑权重对输出的影响和相互作用
        6. 连接重要性评估：基于信息理论的连接评分
        7. 层级自适应：根据层类型调整策略

        Returns:
            importance_scores: 权重重要性分数
        """
        # 获取激活统计特征
        variance = activation_stats['var']         # [in_features] - 梯度近似主要项
        skewness = torch.abs(activation_stats['skewness'])  # [in_features] - 分布不对称性
        entropy = activation_stats['entropy']      # [in_features] - 信息丰富度
        mean_abs = torch.abs(activation_stats['mean'])  # [in_features] - 激活偏移
        l2_norm = activation_stats['l2_norm']      # [in_features] - 激活幅度（融合Wanda）

        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]
        weight_squared = weight_matrix ** 2    # [out_features, in_features]

        # === 1. 经典SNIP分量：权重×方差（梯度近似）===
        grad_approx = variance.unsqueeze(0)
        snip_classic = weight_abs * grad_approx

        # === 2. Wanda启发的分量：权重×L2范数 ===
        # 融合Wanda的激活感知机制
        wanda_scale = torch.sqrt(l2_norm + 1e-8).unsqueeze(0)
        wanda_inspired = weight_abs * wanda_scale

        # === 3. SparseGPT启发的二阶分量：权重²/方差 ===
        # 借鉴SparseGPT的重要性计算，用方差近似Hessian
        hessian_approx = variance + 1e-8
        sparsegpt_inspired = weight_squared / hessian_approx.unsqueeze(0)

        # === 4. 高阶梯度近似：权重×(方差×偏度) ===
        # 偏度反映激活分布的不对称性，高偏度表示梯度变化更剧烈
        higher_order_grad = variance * (1 + skewness)
        higher_grad_scale = higher_order_grad.unsqueeze(0)
        higher_component = weight_abs * higher_grad_scale

        # === 5. 信息敏感性：权重×熵 ===
        # 高熵表示激活包含更多信息，对应连接更重要
        info_scale = entropy.unsqueeze(0)
        info_component = weight_abs * info_scale

        # === 6. 激活幅度敏感性：权重×激活均值 ===
        # 考虑激活的基础幅度对梯度的影响
        magnitude_scale = mean_abs.unsqueeze(0)
        magnitude_component = weight_abs * magnitude_scale

        # === 7. 连接结构分析（借鉴SparseGPT的权重相互作用思想）===
        # 7.1 输出连接重要性：权重在输出维度的相对重要性
        output_importance = weight_abs / (torch.sum(weight_abs, dim=1, keepdim=True) + 1e-8)

        # 7.2 输入连接重要性：权重在输入维度的相对重要性
        input_importance = weight_abs / (torch.sum(weight_abs, dim=0, keepdim=True) + 1e-8)

        # 7.3 连接交互项
        connection_interaction = output_importance * input_importance

        # === 8. 层级自适应权重分配 ===
        if layer_info:
            layer_name = layer_info.get('layer_name', '')
            if 'q_proj' in layer_name or 'k_proj' in layer_name:
                # 注意力层：平衡SNIP梯度、Wanda激活和SparseGPT二阶信息
                w_snip, w_wanda, w_sparsegpt, w_higher, w_info, w_mag, w_struct = 0.25, 0.20, 0.15, 0.20, 0.10, 0.05, 0.05
            elif 'gate_proj' in layer_name:
                # 门控层：重视SNIP高阶梯度和SparseGPT精确性
                w_snip, w_wanda, w_sparsegpt, w_higher, w_info, w_mag, w_struct = 0.20, 0.15, 0.25, 0.25, 0.10, 0.03, 0.02
            elif 'down_proj' in layer_name:
                # 下投影：重视连接结构和SparseGPT（可以更激进剪枝）
                w_snip, w_wanda, w_sparsegpt, w_higher, w_info, w_mag, w_struct = 0.15, 0.15, 0.25, 0.15, 0.10, 0.05, 0.15
            elif 'v_proj' in layer_name:
                # Value层：重视Wanda激活感知
                w_snip, w_wanda, w_sparsegpt, w_higher, w_info, w_mag, w_struct = 0.20, 0.30, 0.15, 0.20, 0.10, 0.03, 0.02
            elif 'o_proj' in layer_name:
                # 输出层：重视连接结构和SparseGPT
                w_snip, w_wanda, w_sparsegpt, w_higher, w_info, w_mag, w_struct = 0.20, 0.15, 0.25, 0.15, 0.10, 0.05, 0.10
            else:
                # 默认权重：平衡三种方法
                w_snip, w_wanda, w_sparsegpt, w_higher, w_info, w_mag, w_struct = 0.25, 0.20, 0.20, 0.20, 0.10, 0.03, 0.02
        else:
            w_snip, w_wanda, w_sparsegpt, w_higher, w_info, w_mag, w_struct = 0.25, 0.20, 0.20, 0.20, 0.10, 0.03, 0.02

        # === 9. 分量融合 ===
        # 使用稳健归一化（借鉴SparseGPT的数值稳定性）
        def robust_normalize(tensor):
            percentile_90 = torch.quantile(tensor.flatten(), 0.90)
            return tensor / (percentile_90 + 1e-8)

        snip_norm = robust_normalize(snip_classic)
        wanda_norm = robust_normalize(wanda_inspired)
        sparsegpt_norm = robust_normalize(sparsegpt_inspired)
        higher_norm = robust_normalize(higher_component)
        info_norm = robust_normalize(info_component)
        mag_norm = robust_normalize(magnitude_component)
        struct_norm = robust_normalize(connection_interaction)

        # 融合三种方法的优点
        importance_scores = (w_snip * snip_norm +
                           w_wanda * wanda_norm +
                           w_sparsegpt * sparsegpt_norm +
                           w_higher * higher_norm +
                           w_info * info_norm +
                           w_mag * mag_norm +
                           w_struct * struct_norm)

        # === 10. 最终稳定性处理（借鉴SparseGPT的阻尼机制）===
        # 添加小的阻尼项以避免数值不稳定
        damp_factor = 0.01 * torch.mean(importance_scores)
        importance_scores = importance_scores + damp_factor

        # 确保非负性
        importance_scores = torch.clamp(importance_scores, min=1e-8)

        return importance_scores

    @classmethod
    def get_predictor(cls, strategy="dolphin"):
        """
        获取指定策略的预测器

        Args:
            strategy: 预测器策略名称
                - "dolphin": 增强预测器
                - "wanda": Wanda预测器
                - "snip": SNIP预测器

        Returns:
            predictor: 预测器函数
        """
        strategy_map = {
            "dolphin": cls.dolphin_predictor,
            "wanda": cls.wanda_predictor,
            "snip": cls.snip_predictor
        }
        
        if strategy in strategy_map:
            return strategy_map[strategy]
        else:
            available = list(strategy_map.keys())
            raise ValueError(f"未知策略 '{strategy}'，可用策略: {available}")

    @classmethod
    def list_available_strategies(cls):
        """获取所有可用的预测器策略"""
        return ["dolphin", "wanda", "snip"]
